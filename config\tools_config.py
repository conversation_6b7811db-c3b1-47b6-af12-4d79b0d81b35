"""
Конфигурация для включения и отключения AI инструментов проекта.

Этот файл определяет, какие инструменты доступны в системе Tool Calling.
Каждый инструмент может быть включен или отключен через флаг 'enabled'.

Структура конфигурации:
{
    "tool_name": {
        "module": "путь.к.модулю",
        "class": "ИмяКласса",
        "enabled": True/False,
        "description": "Описание инструмента"
    }
}
"""

# Конфигурация доступных инструментов
TOOLS_CONFIG = {
    # Генерация изображений (активный инструмент)
    "image_generation": {
        "module": "app.tools.implementations.image_generator_tool",
        "class": "ImageGeneratorTool",
        "enabled": True,
        "description": "Генерация изображений по текстовому описанию"
    },
    # Редактирование изображений (новый инструмент)
    "image_editing": {
        "module": "app.tools.implementations.edit_image_tool",
        "class": "EditImageTool",
        "enabled": True,
        "description": "Редактирование последнего активного изображения"
    }
}

# Функция для получения только активных инструментов
def get_enabled_tools():
    """Возвращает словарь только включенных инструментов."""
    return {name: config for name, config in TOOLS_CONFIG.items()
            if config.get('enabled', False)}