#!/usr/bin/env python3
"""
Тест для проверки инструмента редактирования изображений.
Этот тест проверяет, что новый инструмент корректно загружается и имеет правильную структуру.
"""

import asyncio
import sys
import os

# Добавляем корневую директорию проекта в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.tools.registry import ToolRegistry
from config.tools_config import TOOLS_CONFIG


async def test_edit_image_tool_loading():
    """
    Тестирует загрузку инструмента редактирования изображений.
    """
    print("🧪 Тестирование загрузки инструмента edit_image...")
    
    # Создаем реестр инструментов
    registry = ToolRegistry()
    
    # Загружаем инструменты
    await registry.load_tools()
    
    # Проверяем, что инструмент edit_image загружен
    tools = registry.get_enabled_tools()
    
    if "edit_image" in tools:
        print("✅ Инструмент edit_image успешно загружен!")
        
        tool = tools["edit_image"]
        print(f"   Имя: {tool.name}")
        print(f"   Описание: {tool.description}")
        print(f"   Схема: {tool.schema}")
        
        # Проверяем схему OpenAI
        openai_schemas = registry.get_openai_tools_schema()
        edit_image_schema = None
        
        for schema in openai_schemas:
            if schema["function"]["name"] == "edit_image":
                edit_image_schema = schema
                break
        
        if edit_image_schema:
            print("✅ Схема OpenAI для edit_image сформирована корректно!")
            print(f"   Схема: {edit_image_schema}")
        else:
            print("❌ Схема OpenAI для edit_image не найдена!")
            
    else:
        print("❌ Инструмент edit_image не загружен!")
        print(f"   Доступные инструменты: {list(tools.keys())}")
    
    print(f"\nВсего загружено инструментов: {registry.get_tools_count()}")
    print(f"Конфигурация содержит инструментов: {len(TOOLS_CONFIG)}")


async def test_tool_configuration():
    """
    Тестирует конфигурацию инструмента.
    """
    print("\n🧪 Тестирование конфигурации инструмента...")
    
    if "image_editing" in TOOLS_CONFIG:
        config = TOOLS_CONFIG["image_editing"]
        print("✅ Конфигурация image_editing найдена!")
        print(f"   Модуль: {config['module']}")
        print(f"   Класс: {config['class']}")
        print(f"   Включен: {config['enabled']}")
        print(f"   Описание: {config['description']}")
    else:
        print("❌ Конфигурация image_editing не найдена!")


async def main():
    """
    Основная функция тестирования.
    """
    print("🚀 Запуск тестов инструмента редактирования изображений...\n")
    
    await test_tool_configuration()
    await test_edit_image_tool_loading()
    
    print("\n✅ Тестирование завершено!")


if __name__ == "__main__":
    asyncio.run(main())
